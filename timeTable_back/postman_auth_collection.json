{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Timetable Auth API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"testpass\"\n}"}, "url": {"raw": "http://localhost:8080/auth/login", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["auth", "login"]}}}, {"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newuser\",\n  \"password\": \"newpass\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "http://localhost:8080/auth/register", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["auth", "register"]}}}, {"name": "验证Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:8080/auth/validate", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["auth", "validate"]}}}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:8080/auth/me", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["auth", "me"]}}}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:8080/auth/logout", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["auth", "logout"]}}}]}
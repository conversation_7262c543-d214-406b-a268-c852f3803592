<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://nexus.yangqianguan.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="deploySnapshot" />
      <option name="name" value="deploySnapshot" />
      <option name="url" value="https://nexus.yangqianguan.com/repository/yqg-snapshot/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="internal" />
      <option name="name" value="internal" />
      <option name="url" value="https://nexus.yangqianguan.com/repository/yqg-internal/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="deployRelease" />
      <option name="name" value="deployRelease" />
      <option name="url" value="https://nexus.yangqianguan.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://nexus.yangqianguan.com/repository/maven-public/" />
    </remote-repository>
  </component>
</project>
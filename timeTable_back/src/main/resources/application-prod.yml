server:
  port: 8088
  servlet:
    context-path: /timetable/api

spring:
  profiles:
    active: prod
  application:
    name: timetable-backend

  datasource:
    url: ************************************************************************************************************************************************
    username: timetable
    password: <PERSON><PERSON>i*0217
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000


{"client": "Thunder Client", "collectionName": "Timetable Auth API", "dateExported": "2024-06-09T12:00:00.000Z", "version": "1.1", "folders": [], "requests": [{"_id": "1", "name": "用户登录", "method": "POST", "url": "http://localhost:8080/auth/login", "headers": [{"name": "Content-Type", "value": "application/json"}], "body": {"type": "json", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"testpass\"\n}"}, "tests": []}, {"_id": "2", "name": "用户注册", "method": "POST", "url": "http://localhost:8080/auth/register", "headers": [{"name": "Content-Type", "value": "application/json"}], "body": {"type": "json", "raw": "{\n  \"username\": \"newuser\",\n  \"password\": \"newpass\",\n  \"email\": \"<EMAIL>\"\n}"}, "tests": []}, {"_id": "3", "name": "验证Token", "method": "GET", "url": "http://localhost:8080/auth/validate", "headers": [{"name": "Authorization", "value": "Bearer {{token}}"}], "tests": []}, {"_id": "4", "name": "获取当前用户信息", "method": "GET", "url": "http://localhost:8080/auth/me", "headers": [{"name": "Authorization", "value": "Bearer {{token}}"}], "tests": []}, {"_id": "5", "name": "用户登出", "method": "POST", "url": "http://localhost:8080/auth/logout", "headers": [{"name": "Authorization", "value": "Bearer {{token}}"}], "tests": []}]}